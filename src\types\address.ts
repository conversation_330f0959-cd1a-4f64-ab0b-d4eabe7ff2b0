export type ProducedBlock = {
  number: string;
  age: string;
  transactions: number;
  difficulty: string;
  gasUsed: {
    amount: string;
    percentage: number;
  };
  reward: string;
};

export type AnalyticsData = {
  ethHighest: {
    value: string;
    date: string;
  };
  ethLowest: {
    value: string;
    date: string;
  };
  usdHighest: {
    value: string;
    date: string;
  };
  usdLowest: {
    value: string;
    date: string;
  };
  timeSeriesData: Array<{
    date: string;
    ethBalance: number;
    usdBalance: number;
  }>;
};
